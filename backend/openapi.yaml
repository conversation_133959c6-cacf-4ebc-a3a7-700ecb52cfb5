openapi: 3.0.1
info:
  title: "Choreo Task Management API"
  version: "1.0.0"
  description: "RESTful API service for task management with user authentication."
servers:
- url: "{server}:{port}"
  variables:
    port:
      default: '8080'
    server:
      default: http://localhost

paths:
  /:
    get:
      summary: "Root Endpoint"
      description: "Root endpoint that provides API information and status."
      responses:
        '200':
          description: "API information and status."
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RootResponse'

  /health:
    get:
      summary: "Health Check"
      description: "A simple endpoint to verify that the service is running."
      responses:
        '200':
          description: "Service is available."
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'

  /api/tasks:
    get:
      summary: "Get Tasks"
      description: "Retrieve all tasks for the authenticated user."
      responses:
        '200':
          description: "Tasks retrieved successfully."
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskListResponse'
        '400':
          description: "Bad Request"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    post:
      summary: "Create Task"
      description: "Create a new task for the authenticated user."
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTaskRequest'
      responses:
        '200':
          description: "Task created successfully."
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskResponse'
        '400':
          description: "Bad Request - Required fields missing."
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'



components:
  schemas:
    CreateTaskRequest:
      type: object
      properties:
        title:
          type: string
          description: "The task title."
          example: "Complete project documentation"
        description:
          type: string
          description: "The task description."
          example: "Write comprehensive documentation for the project"
      required:
        - title

    TaskResponse:
      type: object
      properties:
        message:
          type: string
          description: "The server's confirmation message."
          example: "Task created successfully"
        task:
          $ref: '#/components/schemas/Task'

    Task:
      type: object
      properties:
        id:
          type: string
          description: "Task ID"
          example: "123e4567-e89b-12d3-a456-426614174000"
        title:
          type: string
          description: "Task title"
          example: "Complete project documentation"
        description:
          type: string
          description: "Task description"
          example: "Write comprehensive documentation for the project"

    TaskListResponse:
      type: object
      properties:
        tasks:
          type: array
          items:
            $ref: '#/components/schemas/Task'

    RootResponse:
      type: object
      properties:
        message:
          type: string
          description: "API name and description"
          example: "Choreo Task Management API"
        version:
          type: string
          description: "API version"
          example: "1.0.0"
        status:
          type: string
          description: "Service status"
          example: "running"
        timestamp:
          type: string
          format: date-time
          description: "Current timestamp"
          example: "2025-07-09T15:30:00.123Z"

    HealthResponse:
      type: object
      properties:
        status:
          type: string
          description: "Service status"
          example: "healthy"
        timestamp:
          type: string
          format: date-time
          description: "Current timestamp"
          example: "2025-07-09T15:30:00.123Z"

    ErrorResponse:
      type: object
      properties:
        error:
          type: string
          description: "A description of the error that occurred."
          example: "Title is required."

